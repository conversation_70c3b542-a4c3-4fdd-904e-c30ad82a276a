// Copyright 2023 The Moonfield Developers
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Note: This is an initial Rust port of the C++ StructureOfArrays concept.
// It simplifies some aspects (e.g., memory management, iterators) for a first pass.

use std::marker::PhantomData;

/// Trait to define the types of elements that can be stored in a StructureOfArrays.
/// This helps in creating a generic SoA over tuples of types.
pub trait SoaElementTypes: Sized {
    /// The storage type, typically a tuple of Vecs, e.g., (Vec<T1>, Vec<T2>).
    type Storage;
    /// The type of an item when borrowed, e.g., (&'a T1, &'a T2).
    type RefItem<'a> where Self: 'a;
    /// The type of an item when borrowed mutably, e.g., (&'a mut T1, &'a mut T2).
    type MutRefItem<'a> where Self: 'a;
    /// The type of an item when owned, e.g., (T1, T2).
    type OwnedItem;

    /// Creates new, empty storage.
    fn new_storage() -> Self::Storage;
    /// Creates storage with a specified capacity.
    fn with_capacity_storage(capacity: usize) -> Self::Storage;
    /// Returns the number of items in storage (length of the SoA).
    fn len_storage(storage: &Self::Storage) -> usize;
    /// Returns the capacity of the storage.
    fn capacity_storage(storage: &Self::Storage) -> usize;
    /// Reserves capacity for at least `additional` more elements to be inserted.
    fn reserve_storage(storage: &mut Self::Storage, additional: usize);
    /// Pushes an owned item into storage.
    fn push_to_storage(storage: &mut Self::Storage, item: Self::OwnedItem);
    /// Pops an owned item from storage.
    fn pop_from_storage(storage: &mut Self::Storage) -> Option<Self::OwnedItem>;
    /// Clears the storage, removing all values.
    fn clear_storage(storage: &mut Self::Storage);
    // TODO: Methods for element access, iteration, swap, etc.
}



// Macro to implement SoaElementTypes for tuples of various arities
macro_rules! impl_soa_element_types {
    // Base case for recursion (empty tuple - though not practically used for SoA)
    () => {};

    // General case for N-tuples
    ($($T:ident),+) => {
        impl<$($T),+> SoaElementTypes for ($($T),+) {
            type Storage = ($(Vec<$T>),+);
            type RefItem<'a> = ($(&'a $T),+) where $($T: 'a),+;
            type MutRefItem<'a> = ($(&'a mut $T),+) where $($T: 'a),+;
            type OwnedItem = ($($T),+);

            fn new_storage() -> Self::Storage {
                ($(Vec::<$T>::new()),+)
            }

            fn with_capacity_storage(capacity: usize) -> Self::Storage {
                ($(Vec::<$T>::with_capacity(capacity)),+)
            }

            #[allow(unused_variables)] // storage might be unused if tuple is empty, but we start from T1
            fn len_storage(storage: &Self::Storage) -> usize {
                let ($($T_val),+) = storage;
                // All Vecs are assumed to have the same length. Pick the first one.
                // This requires at least one element in the tuple.
                let arr = [$($T_val.len()),+];
                arr[0]
            }

            #[allow(unused_variables)]
            fn capacity_storage(storage: &Self::Storage) -> usize {
                let ($($T_val),+) = storage;
                // Return capacity of the first Vec, assuming consistent management.
                let arr = [$($T_val.capacity()),+];
                arr[0]
            }

            fn reserve_storage(storage: &mut Self::Storage, additional: usize) {
                let ($($T_val),+) = storage;
                $($T_val.reserve(additional);)+ 
            }

            fn push_to_storage(storage: &mut Self::Storage, item: Self::OwnedItem) {
                let ($($T_storage),+) = storage;
                let ($($T_item),+) = item;
                $($T_storage.push($T_item);)+ 
            }

            fn pop_from_storage(storage: &mut Self::Storage) -> Option<Self::OwnedItem> {
                if Self::len_storage(storage) == 0 {
                    None
                } else {
                    let ($($T_val),+) = storage;
                    // Unwraps are safe if len > 0 and Vecs are kept in sync.
                    Some(($($T_val.pop().unwrap()),+))
                }
            }

            fn clear_storage(storage: &mut Self::Storage) {
                let ($($T_val),+) = storage;
                $($T_val.clear();)+
            }

            fn swap_in_storage(storage: &mut Self::Storage, index1: usize, index2: usize) {
                // This requires that all Vecs in the storage tuple have a swap method.
                // We need to call swap on each Vec individually.
                // This is tricky to do generically with a tuple without further helper traits or macros.
                // Let's assume we can iterate or access them. For now, a placeholder.
                // This will be implemented by the macro for specific arities.
                let ($($T_val),+) = storage;
                $($T_val.swap(index1, index2);)+ 
            }
        }
    };
}

// Implement SoaElementTypes for tuples of 1 to (e.g.) 12 elements
impl_soa_element_types!(T1);
impl_soa_element_types!(T1, T2); // Already manually implemented, macro will overwrite if placed after
impl_soa_element_types!(T1, T2, T3);
impl_soa_element_types!(T1, T2, T3, T4);
impl_soa_element_types!(T1, T2, T3, T4, T5);
impl_soa_element_types!(T1, T2, T3, T4, T5, T6);
// Add more as needed, up to Rust's tuple limit (usually 12 or 16 without features)

// Remove the manual implementation for (T1, T2) if the macro is placed before it
// or ensure the macro-generated one is used by ordering.
// For this example, we'll assume the macro is comprehensive enough.
// The manual impl<T1, T2> ... can be removed.

// Helper trait and macro for accessing tuple fields by const generic index.
// This is a common pattern to emulate HList-like access or variadic generics for tuples.

/// Trait to access the Nth field of a tuple-like structure (e.g., Storage).
pub trait TupleFieldAccess<const FIELD_INDEX: usize> {
    type FieldType;
    fn get_field(storage: &Self) -> &Vec<Self::FieldType>;
    fn get_field_mut(storage: &mut Self) -> &mut Vec<Self::FieldType>;
    fn swap_elements(storage: &mut Self, index1: usize, index2: usize);
}

macro_rules! impl_tuple_field_access_for_storage {
    // Base case for recursion - not strictly needed if we always call with specific indices
    ($idx:expr, $target_idx:expr, $head_ty:ty, $head_val:ident, ) => {
        // This branch handles the case where we are trying to access an out-of-bounds index
        // or to provide a more specific implementation for the last element if needed.
        // For simplicity, we'll rely on the direct access for the correct index.
    };

    // Recursive step for accessing the target field
    ($idx:expr, $target_idx:expr, $head_ty:ty, $head_val:ident, $($rest_ty:ty, $rest_val:ident,)*) => {
        #[allow(unused_variables)]
        impl<$head_ty, $($rest_ty),*> TupleFieldAccess<$idx> for ($($rest_ty,)* Vec<$head_ty>) {
            type FieldType = $head_ty;
            fn get_field(storage: &Self) -> &Vec<Self::FieldType> {
                &storage.$idx // Direct tuple indexing
            }
            fn get_field_mut(storage: &mut Self) -> &mut Vec<Self::FieldType> {
                &mut storage.$idx // Direct tuple indexing
            }
            fn swap_elements(storage: &mut Self, index1: usize, index2: usize) {
                 storage.$idx.swap(index1, index2);
            }
        }
        // Recurse for the next field index, if needed for a different style of macro.
        // However, for direct indexing, we just need one impl per arity and index.
    };
}

// Manual implementations for TupleFieldAccess for Storage types
// This is more straightforward than a complex recursive macro for this specific use case.
macro_rules! impl_soa_storage_access_for_arity {
    ($($T:ident),+; $($idx:tt),+) => {
        $( 
            impl<$($T),+> TupleFieldAccess<$idx> for ($(Vec<$T>),+) {
                type FieldType = tuple_element_type!($idx, $($T),+);
                fn get_field(storage: &Self) -> &Vec<Self::FieldType> {
                    &storage.$idx
                }
                fn get_field_mut(storage: &mut Self) -> &mut Vec<Self::FieldType> {
                    &mut storage.$idx
                }
                fn swap_elements(storage: &mut Self, index1: usize, index2: usize) {
                    // This method is on TupleFieldAccess, but swap should apply to all fields.
                    // This specific swap_elements is for a single field. SoaElementTypes will handle all.
                    storage.$idx.swap(index1, index2);
                }
            }
        )+
    };
}

// Helper macro to get the type of the Nth element in a type list
macro_rules! tuple_element_type {
    (0, $T0:ident, $($TRest:ident),*) => { $T0 };
    (1, $T0:ident, $T1:ident, $($TRest:ident),*) => { $T1 };
    (2, $T0:ident, $T1:ident, $T2:ident, $($TRest:ident),*) => { $T2 };
    (3, $T0:ident, $T1:ident, $T2:ident, $T3:ident, $($TRest:ident),*) => { $T3 };
    (4, $T0:ident, $T1:ident, $T2:ident, $T3:ident, $T4:ident, $($TRest:ident),*) => { $T4 };
    (5, $T0:ident, $T1:ident, $T2:ident, $T3:ident, $T4:ident, $T5:ident, $($TRest:ident),*) => { $T5 };
    // Add more as needed
}

impl_soa_storage_access_for_arity!(T1; 0);
impl_soa_storage_access_for_arity!(T1, T2; 0, 1);
impl_soa_storage_access_for_arity!(T1, T2, T3; 0, 1, 2);
impl_soa_storage_access_for_arity!(T1, T2, T3, T4; 0, 1, 2, 3);
impl_soa_storage_access_for_arity!(T1, T2, T3, T4, T5; 0, 1, 2, 3, 4);
impl_soa_storage_access_for_arity!(T1, T2, T3, T4, T5, T6; 0, 1, 2, 3, 4, 5);



/// Structure of Arrays (SoA).
/// Stores columns of data, where each column is a Vec of a specific type.
/// All columns are kept at the same length.
pub struct StructureOfArrays<Elements: SoaElementTypes> {
    storage: Elements::Storage,
    _marker: PhantomData<Elements>, // If Elements itself is not used directly in struct fields
}

impl<Elements: SoaElementTypes> StructureOfArrays<Elements> {
    /// Creates a new, empty SoA.
    pub fn new() -> Self {
        Self {
            storage: Elements::new_storage(),
            _marker: PhantomData,
        }
    }

    /// Creates a new SoA with a specified initial capacity for its columns.
    pub fn with_capacity(capacity: usize) -> Self {
        Self {
            storage: Elements::with_capacity_storage(capacity),
            _marker: PhantomData,
        }
    }

    /// Returns the number of elements (rows) in the SoA.
    pub fn len(&self) -> usize {
        Elements::len_storage(&self.storage)
    }

    /// Returns `true` if the SoA contains no elements.
    pub fn is_empty(&self) -> bool {
        self.len() == 0
    }

    /// Returns the current capacity of the SoA.
    /// Note: Behavior might differ from C++ version if columns reallocate independently.
    pub fn capacity(&self) -> usize {
        Elements::capacity_storage(&self.storage)
    }

    /// Reserves capacity for at least `additional` more elements to be inserted.
    pub fn reserve(&mut self, additional: usize) {
        Elements::reserve_storage(&mut self.storage, additional);
    }

    /// Appends an element to the back of the SoA.
    ///
    /// # Panics
    /// Panics if the new capacity exceeds `isize::MAX` bytes.
    pub fn push(&mut self, item: Elements::OwnedItem) {
        // C++ `push_back` calls `ensureCapacity`. Vec::push handles its own growth.
        // If specific growth strategy (e.g., (needed * 3 + 1) / 2) is required,
        // it should be implemented here or in `reserve`.
        Elements::push_to_storage(&mut self.storage, item);
    }

    /// Removes the last element from the SoA and returns it, or `None` if it is empty.
    pub fn pop(&mut self) -> Option<Elements::OwnedItem> {
        Elements::pop_from_storage(&mut self.storage)
    }

    /// Clears the SoA, removing all values.
    pub fn clear(&mut self) {
        Elements::clear_storage(&mut self.storage);
    }

    // --- Methods from C++ to consider for porting ---
    // getArrayCount() -> usize (compile time)
    // getNeededSize(size) -> usize (related to memory layout, less relevant for Vec-based approach)
    // Iterators: iter(), iter_mut(), into_iter()
    //   C++: IteratorValueRef, IteratorValue, Iterator (complex)
    //   Rust: Standard iterators yielding RefItem, MutRefItem, OwnedItem
    // setCapacity(capacity) -> C++ specific memory reallocation. Rust: `shrink_to_fit` + `reserve`
    // ensureCapacity(needed) -> `reserve` if needed > capacity
    // resize(needed, value) or resize_with(needed, || Default::default())
    // swap(index1, index2)
    // data<N>() -> &[TN] / &mut [TN]
    // begin<N>(), end<N>() -> same as data()
    // slice<N>() -> same as data()
    // elementAt<N>(index) -> &TN / &mut TN
    // back<N>() -> &TN / &mut TN

    /// Provides access to the underlying storage as slices for a specific column (element type).
    /// This is a simplified version of C++ `data<N>()`, `begin<N>()`, `end<N>()`, `slice<N>()`.
    ///
    /// Example: `soa.column_slice::<0>()` would return `&[T1]` if `Elements` is `(T1, T2, ...) `.
    /// This requires a way to map a const generic `N` to tuple access.
    /// This is tricky in stable Rust without specific traits for each index.
    /// A common approach is to define helper traits like `TupleGet<N>`.
    /// For simplicity here, we might offer direct access methods if tuple arity is fixed or small,
    /// or use a macro to generate `column_0_slice()`, `column_1_slice()`, etc.

    // Placeholder for direct column access methods (to be generated or manually added for specific arities)
    // pub fn column_0_slice(&self) -> &[/* type of first element */] { ... }
    // pub fn column_0_slice_mut(&mut self) -> &mut [/* type of first element */] { ... }

    /// Provides a reference to an element at a specific index in a specific column.
    /// Equivalent to C++ `elementAt<ElementIndex>(index)`.
    /// This also faces challenges with generic indexing `ElementIndex` in stable Rust.
    /// We can provide methods like `element_0_at(index)`, `element_1_at(index)`.

    /// Provides a reference to an element at a specific index in a specific column (field).
    /// FIELD_INDEX is the 0-based index of the field in the tuple `Elements`.
    pub fn element_at_field<const FIELD_INDEX: usize>(&self, entity_index: usize) -> Option<&<Elements::Storage as TupleFieldAccess<FIELD_INDEX>>::FieldType>
    where
        Elements::Storage: TupleFieldAccess<FIELD_INDEX>
    {
        let field_vec: &Vec<<Elements::Storage as TupleFieldAccess<FIELD_INDEX>>::FieldType> = 
            Elements::Storage::get_field(&self.storage);
        field_vec.get(entity_index)
    }

    /// Provides a mutable reference to an element at a specific index in a specific column (field).
    pub fn element_at_field_mut<const FIELD_INDEX: usize>(&mut self, entity_index: usize) -> Option<&mut <Elements::Storage as TupleFieldAccess<FIELD_INDEX>>::FieldType>
    where
        Elements::Storage: TupleFieldAccess<FIELD_INDEX>
    {
        let field_vec: &mut Vec<<Elements::Storage as TupleFieldAccess<FIELD_INDEX>>::FieldType> = 
            Elements::Storage::get_field_mut(&mut self.storage);
        field_vec.get_mut(entity_index)
    }

    /// Provides a slice view into a specific column (field) of the SoA.
    pub fn column_slice_field<const FIELD_INDEX: usize>(&self) -> &[<Elements::Storage as TupleFieldAccess<FIELD_INDEX>>::FieldType]
    where
        Elements::Storage: TupleFieldAccess<FIELD_INDEX>
    {
        Elements::Storage::get_field(&self.storage).as_slice()
    }

    /// Provides a mutable slice view into a specific column (field) of the SoA.
    pub fn column_slice_field_mut<const FIELD_INDEX: usize>(&mut self) -> &mut [<Elements::Storage as TupleFieldAccess<FIELD_INDEX>>::FieldType]
    where
        Elements::Storage: TupleFieldAccess<FIELD_INDEX>
    {
        Elements::Storage::get_field_mut(&mut self.storage).as_mut_slice()
    }


    /// Swaps two elements in the SoA.
    /// This involves swapping elements in each corresponding `Vec`.
    pub fn swap(&mut self, index1: usize, index2: usize) {
        if index1 < self.len() && index2 < self.len() {
            Elements::swap_in_storage(&mut self.storage, index1, index2);
        }
        // Consider panic or error if indices are out of bounds, similar to Vec::swap.
        // For now, matches Vec::swap behavior (panics on out of bounds from individual Vec swaps).
    }
}

impl<Elements: SoaElementTypes> Default for StructureOfArrays<Elements> {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_soa_basic_operations() {
        let mut soa: StructureOfArrays<(i32, String)> = StructureOfArrays::new();
        assert!(soa.is_empty());
        assert_eq!(soa.len(), 0);

        soa.push((1, "hello".to_string()));
        soa.push((2, "world".to_string()));

        assert!(!soa.is_empty());
        assert_eq!(soa.len(), 2);

        let item2 = soa.pop();
        assert_eq!(item2, Some((2, "world".to_string())));
        assert_eq!(soa.len(), 1);

        let item1 = soa.pop();
        assert_eq!(item1, Some((1, "hello".to_string())));
        assert_eq!(soa.len(), 0);
        assert!(soa.is_empty());

        let item_none = soa.pop();
        assert_eq!(item_none, None);
    }

    #[test]
    fn test_soa_tuple_impls() {
        // Test with a 1-tuple
        let mut soa1: StructureOfArrays<(i32,)> = StructureOfArrays::new();
        soa1.push((-10,));
        assert_eq!(soa1.len(), 1);
        assert_eq!(soa1.pop(), Some((-10,)));

        // Test with a 3-tuple
        let mut soa3: StructureOfArrays<(i32, bool, String)> = StructureOfArrays::new();
        soa3.push((1, true, "a".to_string()));
        soa3.push((2, false, "b".to_string()));
        assert_eq!(soa3.len(), 2);
        assert_eq!(soa3.pop(), Some((2, false, "b".to_string())));
        assert_eq!(soa3.pop(), Some((1, true, "a".to_string())));
    }

    #[test]
    fn test_soa_element_access_and_swap() {
        let mut soa: StructureOfArrays<(i32, char)> = StructureOfArrays::new();
        soa.push((10, 'a'));
        soa.push((20, 'b'));
        soa.push((30, 'c'));

        // Test element_at_field
        assert_eq!(soa.element_at_field::<0>(0), Some(&10));
        assert_eq!(soa.element_at_field::<1>(1), Some(&'b'));
        assert_eq!(soa.element_at_field::<0>(3), None);

        // Test column_slice_field
        assert_eq!(soa.column_slice_field::<0>(), &[10, 20, 30]);
        assert_eq!(soa.column_slice_field::<1>(), &['a', 'b', 'c']);

        // Test element_at_field_mut
        if let Some(val) = soa.element_at_field_mut::<0>(0) {
            *val = 100;
        }
        assert_eq!(soa.element_at_field::<0>(0), Some(&100));

        // Test column_slice_field_mut
        let slice_mut = soa.column_slice_field_mut::<1>();
        slice_mut[2] = 'z';
        assert_eq!(soa.column_slice_field::<1>(), &['a', 'b', 'z']);
        
        // Test swap
        soa.swap(0, 2);
        assert_eq!(soa.column_slice_field::<0>(), &[30, 20, 100]);
        assert_eq!(soa.column_slice_field::<1>(), &['z', 'b', 'a']);

        // Test pop after swap
        assert_eq!(soa.pop(), Some((100, 'a')));
        assert_eq!(soa.column_slice_field::<0>(), &[30, 20]);
        assert_eq!(soa.column_slice_field::<1>(), &['z', 'b']);
    }

    #[test]
    fn test_soa_capacity_reserve() {
        let mut soa: StructureOfArrays<(u8, f32)> = StructureOfArrays::with_capacity(5);
        assert!(soa.capacity() >= 5);
        let initial_cap = soa.capacity();

        for i in 0..5 {
            soa.push((i as u8, i as f32));
        }
        assert_eq!(soa.len(), 5);
        assert_eq!(soa.capacity(), initial_cap);

        // Trigger reallocation by pushing more or reserving
        soa.reserve(10); // Reserve space for 10 *additional* elements
        assert!(soa.capacity() >= initial_cap + 10 || soa.capacity() >= soa.len() + 10);
        // Actual capacity depends on Vec's growth strategy, but it should be at least len + additional requested.
    }
    
    #[test]
    fn test_clear() {
        let mut soa: StructureOfArrays<(i32, bool)> = StructureOfArrays::new();
        soa.push((1, true));
        soa.push((2, false));
        assert_eq!(soa.len(), 2);
        soa.clear();
        assert_eq!(soa.len(), 0);
        assert!(soa.is_empty());
        // Check if capacity is retained (Vec::clear does this)
        assert!(soa.capacity() >= 2 || soa.capacity() == 0); // Capacity might be 0 if it never allocated
    }
}